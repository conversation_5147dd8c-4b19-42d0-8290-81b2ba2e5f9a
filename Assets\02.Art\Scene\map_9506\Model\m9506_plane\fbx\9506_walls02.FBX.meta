fileFormatVersion: 2
guid: e627a8bb06741d24597199a2bebd92cb
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: 9506_walls02_a
    100004: 9506_walls03_b
    100006: 9506_walls02_c
    100008: 9506_walls02_b
    100010: 9506_walls02_a01
    100012: 9506_walls02_a02
    100014: 9506_walls02_a03
    100016: 9506_walls02_a04
    100018: 9506_walls02_a05
    100020: 9506_walls02_a06
    100022: 9506_walls02_b01
    100024: 9506_walls02_b02
    100026: 9506_walls02_b03
    100028: 9506_walls02_b04
    100030: 9506_walls02_b05
    100032: 9506_walls02_b06
    100034: 9506_walls02_c01
    100036: 9506_walls02_c02
    100038: 9506_walls02_c03
    400000: //RootNode
    400002: 9506_walls02_a
    400004: 9506_walls03_b
    400006: 9506_walls02_c
    400008: 9506_walls02_b
    400010: 9506_walls02_a01
    400012: 9506_walls02_a02
    400014: 9506_walls02_a03
    400016: 9506_walls02_a04
    400018: 9506_walls02_a05
    400020: 9506_walls02_a06
    400022: 9506_walls02_b01
    400024: 9506_walls02_b02
    400026: 9506_walls02_b03
    400028: 9506_walls02_b04
    400030: 9506_walls02_b05
    400032: 9506_walls02_b06
    400034: 9506_walls02_c01
    400036: 9506_walls02_c02
    400038: 9506_walls02_c03
    2100000: 08 - Default
    2100002: 10 - Default
    2300000: 9506_walls02_a
    2300002: 9506_walls03_b
    2300004: 9506_walls02_c
    2300006: 9506_walls02_b
    2300008: 9506_walls02_a01
    2300010: 9506_walls02_a02
    2300012: 9506_walls02_a03
    2300014: 9506_walls02_a04
    2300016: 9506_walls02_a05
    2300018: 9506_walls02_a06
    2300020: 9506_walls02_b01
    2300022: 9506_walls02_b02
    2300024: 9506_walls02_b03
    2300026: 9506_walls02_b04
    2300028: 9506_walls02_b05
    2300030: 9506_walls02_b06
    2300032: 9506_walls02_c01
    2300034: 9506_walls02_c02
    2300036: 9506_walls02_c03
    3300000: 9506_walls02_a
    3300002: 9506_walls03_b
    3300004: 9506_walls02_c
    3300006: 9506_walls02_b
    3300008: 9506_walls02_a01
    3300010: 9506_walls02_a02
    3300012: 9506_walls02_a03
    3300014: 9506_walls02_a04
    3300016: 9506_walls02_a05
    3300018: 9506_walls02_a06
    3300020: 9506_walls02_b01
    3300022: 9506_walls02_b02
    3300024: 9506_walls02_b03
    3300026: 9506_walls02_b04
    3300028: 9506_walls02_b05
    3300030: 9506_walls02_b06
    3300032: 9506_walls02_c01
    3300034: 9506_walls02_c02
    3300036: 9506_walls02_c03
    4300000: 9506_walls02_a
    4300002: 9506_walls03_b
    4300004: 9506_walls02_c
    4300006: 9506_walls02_b
    4300008: 9506_walls02_a06
    4300010: 9506_walls02_a01
    4300012: 9506_walls02_a02
    4300014: 9506_walls02_a03
    4300016: 9506_walls02_a04
    4300018: 9506_walls02_a05
    4300020: 9506_walls02_b01
    4300022: 9506_walls02_b02
    4300024: 9506_walls02_b03
    4300026: 9506_walls02_b04
    4300028: 9506_walls02_b05
    4300030: 9506_walls02_b06
    4300032: 9506_walls02_c01
    4300034: 9506_walls02_c02
    4300036: 9506_walls02_c03
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
