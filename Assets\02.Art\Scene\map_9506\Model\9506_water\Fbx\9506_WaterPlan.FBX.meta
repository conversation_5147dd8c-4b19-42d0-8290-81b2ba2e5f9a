fileFormatVersion: 2
guid: 7a8241be39b3c3c429fb65d2bc9b3b8e
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: Object511
    100004: waterplane_A
    100006: waterplane_B
    100008: waterplane_C
    100010: waterplane_D
    100012: waterplane_E
    100014: waterplane_F
    400000: //RootNode
    400002: Object511
    400004: waterplane_A
    400006: waterplane_B
    400008: waterplane_C
    400010: waterplane_D
    400012: waterplane_E
    400014: waterplane_F
    2100000: No Name
    2300000: //RootNode
    2300002: Object511
    2300004: waterplane_A
    2300006: waterplane_B
    2300008: waterplane_C
    2300010: waterplane_D
    2300012: waterplane_E
    2300014: waterplane_F
    3300000: //RootNode
    3300002: Object511
    3300004: waterplane_A
    3300006: waterplane_B
    3300008: waterplane_C
    3300010: waterplane_D
    3300012: waterplane_E
    3300014: waterplane_F
    4300000: 9506_WaterPlan
    4300002: waterplane_D
    4300004: waterplane_B
    4300006: waterplane_C
    4300008: waterplane_E
    4300010: Object511
    4300012: waterplane_A
    4300014: waterplane_F
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: No Name
    second: {fileID: 2100000, guid: 0183fdc266783fa47b8631df0e23e67b, type: 2}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
