fileFormatVersion: 2
guid: eef89c536118e784bb108d5fefc8b3c1
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: 9505_building_04a
    100004: 9505_building_04b
    100006: 9505_building_04c
    100008: 9505_building_04d
    100010: 9505_building_04e
    400000: //RootNode
    400002: 9505_building_04a
    400004: 9505_building_04b
    400006: 9505_building_04c
    400008: 9505_building_04d
    400010: 9505_building_04e
    2100000: lambert4
    2100002: 9505_building_04a
    2300000: //RootNode
    2300002: 9505_building_04a
    2300004: 9505_building_04b
    2300006: 9505_building_04c
    2300008: 9505_building_04d
    2300010: 9505_building_04e
    3300000: //RootNode
    3300002: 9505_building_04a
    3300004: 9505_building_04b
    3300006: 9505_building_04c
    3300008: 9505_building_04d
    3300010: 9505_building_04e
    4300000: pCube3
    4300002: 9505_building_04c
    4300004: 9505_building_04a
    4300006: 9505_building_04b
    4300008: 9505_building_04d
    4300010: 9505_building_04e
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: 9505_building_04a
    second: {fileID: 2100000, guid: 4940b4cf7dfcce34393654caac4e1f1d, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: lambert4
    second: {fileID: 2100000, guid: 4940b4cf7dfcce34393654caac4e1f1d, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
