fileFormatVersion: 2
guid: 36fc3c86809d69142bbfa5fcee2cf742
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: 9507_city_wall_c
    100004: 9507_city_wall_c_lod1
    100006: 9507_city_wall_c_main
    100008: 9507_city_wall_c_re
    400000: //RootNode
    400002: 9507_city_wall_c
    400004: 9507_city_wall_c_lod1
    400006: 9507_city_wall_c_main
    400008: 9507_city_wall_c_re
    2100000: 9507_05
    2100002: 9507_01
    2100004: 9507_04
    2100006: 9507_02
    2300000: 9507_city_wall_c
    2300002: 9507_city_wall_c_lod1
    2300004: 9507_city_wall_c_main
    2300006: 9507_city_wall_c_re
    3300000: 9507_city_wall_c
    3300002: 9507_city_wall_c_lod1
    3300004: 9507_city_wall_c_main
    3300006: 9507_city_wall_c_re
    4300000: 9507_city_wall_c
    4300002: 9507_city_wall_c_lod1
    4300004: 9507_city_wall_c_main
    4300006: 9507_city_wall_c_re
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: 9507_01
    second: {fileID: 2100000, guid: 2142c5697c034ca45b89b60b39e7c65c, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: 9507_02
    second: {fileID: 2100000, guid: 80479247e93214e4bacf4c4ef86501a6, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: 9507_04
    second: {fileID: 2100000, guid: c8d18d8fb01e7df48b2b68fbaf47602d, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: 9507_05
    second: {fileID: 2100000, guid: b2810151adb6a3a449b86661947d3827, type: 2}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
