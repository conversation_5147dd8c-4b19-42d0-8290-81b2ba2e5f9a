fileFormatVersion: 2
guid: 6ea058394fdcbbd4ebed45392495063a
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: 9506_wall05_a004
    100002: 9506_wall05_a01
    100004: 9506_wall05_a03
    100006: 9506_wall05_b004
    100008: 9506_wall05_b01
    100010: 9506_wall05_b03
    100012: 9506_wall05_c003
    100014: 9506_wall05_c004
    100016: 9506_wall05_c01
    100018: 9506_wall05_c02
    100020: 9506_wall05_e
    100022: //RootNode
    100024: 9506_wall05_d01
    100026: 9506_wall05_d02
    100028: 9506_wall05_a02
    100030: 9506_wall05_b02
    400000: 9506_wall05_a004
    400002: 9506_wall05_a01
    400004: 9506_wall05_a03
    400006: 9506_wall05_b004
    400008: 9506_wall05_b01
    400010: 9506_wall05_b03
    400012: 9506_wall05_c003
    400014: 9506_wall05_c004
    400016: 9506_wall05_c01
    400018: 9506_wall05_c02
    400020: 9506_wall05_e
    400022: //RootNode
    400024: 9506_wall05_d01
    400026: 9506_wall05_d02
    400028: 9506_wall05_a02
    400030: 9506_wall05_b02
    2100000: 'Material #87'
    2100002: 'Material #567'
    2100004: 18 - Default
    2100006: m9506_palace02
    2100008: 20 - Default
    2300000: 9506_wall05_a004
    2300002: 9506_wall05_a01
    2300004: 9506_wall05_a03
    2300006: 9506_wall05_b004
    2300008: 9506_wall05_b01
    2300010: 9506_wall05_b03
    2300012: 9506_wall05_c003
    2300014: 9506_wall05_c004
    2300016: 9506_wall05_c01
    2300018: 9506_wall05_c02
    2300020: 9506_wall05_e
    2300022: 9506_wall05_d01
    2300024: 9506_wall05_d02
    2300026: 9506_wall05_a02
    2300028: 9506_wall05_b02
    3300000: 9506_wall05_a004
    3300002: 9506_wall05_a01
    3300004: 9506_wall05_a03
    3300006: 9506_wall05_b004
    3300008: 9506_wall05_b01
    3300010: 9506_wall05_b03
    3300012: 9506_wall05_c003
    3300014: 9506_wall05_c004
    3300016: 9506_wall05_c01
    3300018: 9506_wall05_c02
    3300020: 9506_wall05_e
    3300022: 9506_wall05_d01
    3300024: 9506_wall05_d02
    3300026: 9506_wall05_a02
    3300028: 9506_wall05_b02
    4300000: 9506_wall05_a01
    4300002: 9506_wall05_c01
    4300004: 9506_wall05_a03
    4300006: 9506_wall05_b01
    4300008: 9506_wall05_b03
    4300010: 9506_wall05_c02
    4300012: 9506_wall05_e
    4300014: 9506_wall05_a004
    4300016: 9506_wall05_c003
    4300018: 9506_wall05_c004
    4300020: 9506_wall05_b004
    4300022: 9506_wall05_d01
    4300024: 9506_wall05_d02
    4300026: 9506_wall05_b02
    4300028: 9506_wall05_a02
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
