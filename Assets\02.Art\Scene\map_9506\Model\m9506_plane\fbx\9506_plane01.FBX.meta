fileFormatVersion: 2
guid: 559fa5051b8d7414cb63d30af4ec86d4
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: 9506_plane01_a
    100004: 9506_plane01_b
    100006: 9506_plane01_c
    100008: 9506_plane01_d
    100010: 9506_plane01_e
    100012: 9506_plane01_f
    100014: 9506_plane01_g
    100016: 9506_plane01_a01
    100018: 9506_plane01_a02
    100020: 9506_plane01_a03
    400000: //RootNode
    400002: 9506_plane01_a
    400004: 9506_plane01_b
    400006: 9506_plane01_c
    400008: 9506_plane01_d
    400010: 9506_plane01_e
    400012: 9506_plane01_f
    400014: 9506_plane01_g
    400016: 9506_plane01_a01
    400018: 9506_plane01_a02
    400020: 9506_plane01_a03
    2100000: 10 - Default
    2100002: stine_line
    2100004: m9506
    2100006: white block
    2100008: 'Material #567'
    2300000: 9506_plane01_a
    2300002: 9506_plane01_b
    2300004: 9506_plane01_c
    2300006: 9506_plane01_d
    2300008: 9506_plane01_e
    2300010: 9506_plane01_f
    2300012: 9506_plane01_g
    2300014: 9506_plane01_a01
    2300016: 9506_plane01_a02
    2300018: 9506_plane01_a03
    3300000: 9506_plane01_a
    3300002: 9506_plane01_b
    3300004: 9506_plane01_c
    3300006: 9506_plane01_d
    3300008: 9506_plane01_e
    3300010: 9506_plane01_f
    3300012: 9506_plane01_g
    3300014: 9506_plane01_a01
    3300016: 9506_plane01_a02
    3300018: 9506_plane01_a03
    4300000: 9506_plane01_e
    4300002: 9506_plane01_d
    4300004: 9506_plane01_a
    4300006: 9506_plane01_c
    4300008: 9506_plane01_b
    4300010: 9506_plane01_f
    4300012: 9506_plane01_g
    4300014: 9506_plane01_a01
    4300016: 9506_plane01_a02
    4300018: 9506_plane01_a03
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
