fileFormatVersion: 2
guid: 5d2594292af2ba842a0aba1b423f8779
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: floor01
    100004: floor02
    100006: stairs01
    100008: stairs02
    100010: stone01
    100012: stone02
    100014: stone03
    100016: stone04
    100018: stone05
    100020: stone06
    100022: stone07
    100024: stone08
    100026: stone09
    100028: stone10
    100030: stone11
    100032: stone12
    100034: stone13
    100036: stone14
    100038: stone15
    100040: stone16
    100042: stone17
    100044: stone18
    400000: //RootNode
    400002: floor01
    400004: floor02
    400006: stairs01
    400008: stairs02
    400010: stone01
    400012: stone02
    400014: stone03
    400016: stone04
    400018: stone05
    400020: stone06
    400022: stone07
    400024: stone08
    400026: stone09
    400028: stone10
    400030: stone11
    400032: stone12
    400034: stone13
    400036: stone14
    400038: stone15
    400040: stone16
    400042: stone17
    400044: stone18
    2100000: 9506_stairs_m
    2300000: floor01
    2300002: floor02
    2300004: stairs01
    2300006: stairs02
    2300008: stone01
    2300010: stone02
    2300012: stone03
    2300014: stone04
    2300016: stone05
    2300018: stone06
    2300020: stone07
    2300022: stone08
    2300024: stone09
    2300026: stone10
    2300028: stone11
    2300030: stone12
    2300032: stone13
    2300034: stone14
    2300036: stone15
    2300038: stone16
    2300040: stone17
    2300042: stone18
    3300000: floor01
    3300002: floor02
    3300004: stairs01
    3300006: stairs02
    3300008: stone01
    3300010: stone02
    3300012: stone03
    3300014: stone04
    3300016: stone05
    3300018: stone06
    3300020: stone07
    3300022: stone08
    3300024: stone09
    3300026: stone10
    3300028: stone11
    3300030: stone12
    3300032: stone13
    3300034: stone14
    3300036: stone15
    3300038: stone16
    3300040: stone17
    3300042: stone18
    4300000: stairs01
    4300002: floor01
    4300004: stone01
    4300006: stairs02
    4300008: floor02
    4300010: stone18
    4300012: stone15
    4300014: stone17
    4300016: stone16
    4300018: stone14
    4300020: stone12
    4300022: stone13
    4300024: stone11
    4300026: stone10
    4300028: stone08
    4300030: stone09
    4300032: stone06
    4300034: stone07
    4300036: stone05
    4300038: stone04
    4300040: stone03
    4300042: stone02
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
