fileFormatVersion: 2
guid: aad0b798c4e0a1f43a8df8856a7fb13c
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: 9506_walls01_a
    100004: Object362
    100006: 9506_walls01_b
    100008: 9506_walls03_a03
    100010: 9506_walls01_a01
    100012: 9506_walls01_a02
    100014: 9506_walls01_a03
    100016: 9506_walls01_a04
    100018: 9506_walls01_a05
    100020: 9506_walls01_a06
    100022: 9506_walls01_a07
    100024: 9506_walls01_a08
    100026: 9506_walls01_a09
    100028: 9506_walls01_a10
    100030: 9506_walls01_b01
    100032: 9506_walls01_b02
    100034: 9506_walls01_b03
    100036: 9506_walls01_b04
    100038: 9506_walls01_b07
    100040: 9506_walls01_b10
    100042: Object378
    100044: Object382
    100046: 9506_walls01_c01
    100048: 9506_walls01_d01
    100050: 9506_walls01_e01
    100052: 9506_walls01_e02
    100054: 9506_walls01_f01
    100056: 9506_walls01_f02
    100058: 9506_walls01_g01
    100060: 9506_walls01_g02
    400000: //RootNode
    400002: 9506_walls01_a
    400004: Object362
    400006: 9506_walls01_b
    400008: 9506_walls03_a03
    400010: 9506_walls01_a01
    400012: 9506_walls01_a02
    400014: 9506_walls01_a03
    400016: 9506_walls01_a04
    400018: 9506_walls01_a05
    400020: 9506_walls01_a06
    400022: 9506_walls01_a07
    400024: 9506_walls01_a08
    400026: 9506_walls01_a09
    400028: 9506_walls01_a10
    400030: 9506_walls01_b01
    400032: 9506_walls01_b02
    400034: 9506_walls01_b03
    400036: 9506_walls01_b04
    400038: 9506_walls01_b07
    400040: 9506_walls01_b10
    400042: Object378
    400044: Object382
    400046: 9506_walls01_c01
    400048: 9506_walls01_d01
    400050: 9506_walls01_e01
    400052: 9506_walls01_e02
    400054: 9506_walls01_f01
    400056: 9506_walls01_f02
    400058: 9506_walls01_g01
    400060: 9506_walls01_g02
    2100000: 07 - Default
    2300000: //RootNode
    2300002: 9506_walls01_a
    2300004: Object362
    2300006: 9506_walls01_b
    2300008: 9506_walls03_a03
    2300010: 9506_walls01_a01
    2300012: 9506_walls01_a02
    2300014: 9506_walls01_a03
    2300016: 9506_walls01_a04
    2300018: 9506_walls01_a05
    2300020: 9506_walls01_a06
    2300022: 9506_walls01_a07
    2300024: 9506_walls01_a08
    2300026: 9506_walls01_a09
    2300028: 9506_walls01_a10
    2300030: 9506_walls01_b01
    2300032: 9506_walls01_b02
    2300034: 9506_walls01_b03
    2300036: 9506_walls01_b04
    2300038: 9506_walls01_b07
    2300040: 9506_walls01_b10
    2300042: Object378
    2300044: Object382
    2300046: 9506_walls01_c01
    2300048: 9506_walls01_d01
    2300050: 9506_walls01_e01
    2300052: 9506_walls01_e02
    2300054: 9506_walls01_f01
    2300056: 9506_walls01_f02
    2300058: 9506_walls01_g01
    2300060: 9506_walls01_g02
    3300000: //RootNode
    3300002: 9506_walls01_a
    3300004: Object362
    3300006: 9506_walls01_b
    3300008: 9506_walls03_a03
    3300010: 9506_walls01_a01
    3300012: 9506_walls01_a02
    3300014: 9506_walls01_a03
    3300016: 9506_walls01_a04
    3300018: 9506_walls01_a05
    3300020: 9506_walls01_a06
    3300022: 9506_walls01_a07
    3300024: 9506_walls01_a08
    3300026: 9506_walls01_a09
    3300028: 9506_walls01_a10
    3300030: 9506_walls01_b01
    3300032: 9506_walls01_b02
    3300034: 9506_walls01_b03
    3300036: 9506_walls01_b04
    3300038: 9506_walls01_b07
    3300040: 9506_walls01_b10
    3300042: Object378
    3300044: Object382
    3300046: 9506_walls01_c01
    3300048: 9506_walls01_d01
    3300050: 9506_walls01_e01
    3300052: 9506_walls01_e02
    3300054: 9506_walls01_f01
    3300056: 9506_walls01_f02
    3300058: 9506_walls01_g01
    3300060: 9506_walls01_g02
    4300000: 9506_walls01_a
    4300002: Object362
    4300004: 9506_walls01_b
    4300006: 9506_walls03_a03
    4300008: 9506_walls01_a01
    4300010: 9506_walls01_b01
    4300012: 9506_walls01_a02
    4300014: 9506_walls01_a03
    4300016: 9506_walls01_a10
    4300018: 9506_walls01_a09
    4300020: 9506_walls01_a08
    4300022: Object378
    4300024: 9506_walls01_b03
    4300026: 9506_walls01_b02
    4300028: 9506_walls01_b10
    4300030: Object382
    4300032: 9506_walls01_b07
    4300034: 9506_walls01_b04
    4300036: 9506_walls01_a04
    4300038: 9506_walls01_a05
    4300040: 9506_walls01_a06
    4300042: 9506_walls01_a07
    4300044: 9506_walls01_d01
    4300046: 9506_walls01_g02
    4300048: 9506_walls01_f01
    4300050: 9506_walls01_e02
    4300052: 9506_walls01_f02
    4300054: 9506_walls01_g01
    4300056: 9506_walls01_c01
    4300058: 9506_walls01_e01
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
