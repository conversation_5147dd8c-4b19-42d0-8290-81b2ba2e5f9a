%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: spaceship_A
  m_Shader: {fileID: 4800000, guid: c72ad8812185b1f408cf980bf7c0435e, type: 3}
  m_ValidKeywords:
  - _Emi_ON
  m_InvalidKeywords:
  - _EMISSION
  - _METALLICGLOSSMAP
  - _NORMALMAP
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 1e6a49d11c3d47248a8f68b02c9eed2b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissolveTex:
        m_Texture: {fileID: 2800000, guid: 24d73a0978aa8264d8d2eafb48f370bb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 2800000, guid: 6101516d84a509c44886685d2d9c39a3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissiveTex:
        m_Texture: {fileID: 2800000, guid: 90c37341dd51f0d4db2ed7d6e0707465, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 1b0f88427b21ac4468909a8bbb071e3c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: d6b5655f95666b040853383cbd76899e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTex:
        m_Texture: {fileID: 2800000, guid: efb5170dbaabb47439b0d36fef9bcc40, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BumpScale: 1
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DissolveAmount: 1
    - _DissolveScale: 0.7
    - _DstBlend: 0
    - _EdgeThickness: 0.05
    - _EdgeWidth: 0.1
    - _Emi: 1
    - _EmissiveInten: 1.11
    - _FadeEnd: 263.3
    - _FadeStart: 263.12
    - _FadeThreshold: 1
    - _GlossMapScale: 1
    - _Glossiness: 1
    - _GlossyReflections: 1
    - _Metallic: 1
    - _Mode: 0
    - _OccInten: 1
    - _OcclusionStrength: 1
    - _Ocu: 0
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _DissolveColor: {r: 0, g: 17.443138, b: 23.968628, a: 1}
    - _EdgeColor: {r: 1, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissiveColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
