fileFormatVersion: 2
guid: a033dfa1423dee748a623f945cb51c54
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: 9506_stoneplane_j01
    100004: 9506_stoneplane_j02
    100006: 9506_stoneplane_j03
    100008: 9506_stoneplane_j04
    100010: 9506_stoneplane_j05
    100012: 9506_stoneplane_j06
    100014: 9506_stoneplane_j07
    400000: //RootNode
    400002: 9506_stoneplane_j01
    400004: 9506_stoneplane_j02
    400006: 9506_stoneplane_j03
    400008: 9506_stoneplane_j04
    400010: 9506_stoneplane_j05
    400012: 9506_stoneplane_j06
    400014: 9506_stoneplane_j07
    2100000: 'Material #568'
    2100002: 10 - Default
    2100004: m9506
    2100006: white block
    2300000: 9506_stoneplane_j01
    2300002: 9506_stoneplane_j02
    2300004: 9506_stoneplane_j03
    2300006: 9506_stoneplane_j04
    2300008: 9506_stoneplane_j05
    2300010: 9506_stoneplane_j06
    2300012: 9506_stoneplane_j07
    3300000: 9506_stoneplane_j01
    3300002: 9506_stoneplane_j02
    3300004: 9506_stoneplane_j03
    3300006: 9506_stoneplane_j04
    3300008: 9506_stoneplane_j05
    3300010: 9506_stoneplane_j06
    3300012: 9506_stoneplane_j07
    4300000: 9506_stoneplane_j01
    4300002: 9506_stoneplane_j05
    4300004: 9506_stoneplane_j03
    4300006: 9506_stoneplane_j02
    4300008: 9506_stoneplane_j04
    4300010: 9506_stoneplane_j06
    4300012: 9506_stoneplane_j07
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 2
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
